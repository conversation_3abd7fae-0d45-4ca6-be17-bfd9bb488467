# Redis Production Monitoring Guide

## 关键监控指标

### 1. 内存使用情况
```bash
# 检查内存使用
./redis/prod/redis-prod-utils.sh memory

# 关键指标：
# - used_memory: 当前使用内存
# - maxmemory: 最大内存限制 (2GB)
# - mem_fragmentation_ratio: 内存碎片率 (理想值: 1.0-1.5)
# - used_memory_peak: 峰值内存使用
```

### 2. 缓存命中率
```bash
# 检查缓存统计
./redis/prod/redis-prod-utils.sh stats

# 关键指标：
# - keyspace_hits: 缓存命中次数
# - keyspace_misses: 缓存未命中次数
# - 命中率 = hits / (hits + misses) * 100%
# - 理想命中率: > 90%
```

### 3. 音频缓存状态
```bash
# 检查音频缓存
./redis/prod/redis-prod-utils.sh audio

# 监控项：
# - empathy_audio 缓存数量
# - workflow_audio 缓存数量
# - TTL 剩余时间 (应该在 0-900 秒之间)
```

### 4. 性能指标
```bash
# 实时监控延迟
./redis/prod/redis-prod-utils.sh monitor

# 关键指标：
# - 平均延迟: < 1ms
# - 99%延迟: < 5ms
# - 慢查询: 应该很少或没有
```

## 告警阈值建议

### 内存告警
- **警告**: 内存使用率 > 80% (1.6GB)
- **严重**: 内存使用率 > 90% (1.8GB)
- **紧急**: 内存使用率 > 95% (1.9GB)

### 性能告警
- **警告**: 平均延迟 > 2ms
- **严重**: 平均延迟 > 5ms
- **紧急**: 平均延迟 > 10ms

### 缓存告警
- **警告**: 命中率 < 85%
- **严重**: 命中率 < 75%
- **紧急**: 命中率 < 60%

### 连接告警
- **警告**: 连接数 > 8000
- **严重**: 连接数 > 9000
- **紧急**: 连接数 > 9500

## 日常维护任务

### 每日检查
```bash
# 1. 检查Redis状态
./redis/prod/redis-prod-utils.sh status

# 2. 检查内存使用
./redis/prod/redis-prod-utils.sh memory

# 3. 检查音频缓存状态
./redis/prod/redis-prod-utils.sh audio
```

### 每周检查
```bash
# 1. 查看统计信息
./redis/prod/redis-prod-utils.sh stats

# 2. 检查配置
./redis/prod/redis-prod-utils.sh config

# 3. 创建备份
./redis/prod/redis-prod-utils.sh backup
```

## 故障排查

### 内存不足
1. 检查内存使用情况
2. 查看是否有大量过期键未清理
3. 检查是否有内存泄漏
4. 考虑增加内存限制或优化缓存策略

### 性能下降
1. 检查慢查询日志
2. 监控CPU使用率
3. 检查网络延迟
4. 查看是否有大量过期键处理

### 缓存命中率低
1. 检查TTL设置是否合理
2. 分析缓存键的访问模式
3. 考虑调整缓存策略
4. 检查应用程序的缓存逻辑

## 备份策略

### 持久化策略调整
- **已禁用持久化**: 为了最大化性能和最小化磁盘使用
- **数据可接受丢失**: 音频缓存数据可以重新生成
- **无自动备份**: 不再创建RDB快照和AOF日志

### 应急备份 (可选)
```bash
# 仅在特殊情况下手动创建备份
./redis/prod/redis-prod-utils.sh backup
```

### 数据恢复策略
- **重启恢复**: Redis重启后缓存为空，应用会自动重新生成
- **无需备份恢复**: 所有数据都有15分钟TTL，会自动过期

## 扩容建议

### 何时考虑扩容
- 内存使用率持续 > 80% (1.6GB)
- 平均延迟持续 > 2ms
- 缓存命中率持续 < 85%
- 连接数持续 > 8000
- 频繁出现内存不足导致的缓存淘汰

### 扩容选项
1. **垂直扩容**: 增加内存到4GB或8GB (当服务器资源允许时)
2. **水平扩容**: 实施Redis集群
3. **读写分离**: 添加只读副本
4. **缓存优化**: 优化音频文件大小或压缩策略

## 配置优化建议

### 针对音频缓存的优化
- 当前配置已针对大文件缓存优化
- 启用了内存碎片整理
- 使用LRU淘汰策略
- 启用了多线程I/O

### 未来优化方向
- 根据实际使用情况调整内存限制
- 考虑使用Redis模块进行压缩
- 实施缓存预热策略
- 优化过期键清理频率
