#!/bin/bash

# Redis Production Environment Utilities
# This script provides helpful commands for managing Redis in the production environment

set -e

REDIS_CONTAINER="redis"
COMPOSE_FILE="docker-compose.prod.yml"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

# Check if Redis container is running
check_redis_status() {
    if docker ps | grep -q "$REDIS_CONTAINER"; then
        print_status "Redis container is running"
        return 0
    else
        print_error "Redis container is not running"
        return 1
    fi
}

# Start Redis with production configuration
start_redis() {
    print_header "Starting Redis Production Environment"
    docker compose -f "../$COMPOSE_FILE" up -d redis
    sleep 5
    if check_redis_status; then
        print_status "Redis started successfully with production configuration"
        show_memory_info
    else
        print_error "Failed to start Redis"
        exit 1
    fi
}

# Stop Redis
stop_redis() {
    print_header "Stopping Redis Production Environment"
    docker compose -f "../$COMPOSE_FILE" stop redis
    print_status "Redis stopped"
}

# Restart Redis
restart_redis() {
    print_header "Restarting Redis Production Environment"
    stop_redis
    start_redis
}

# Connect to Redis CLI
connect_redis() {
    print_header "Connecting to Redis CLI"
    if check_redis_status; then
        docker exec -it "$REDIS_CONTAINER" redis-cli
    else
        print_error "Redis container is not running"
        exit 1
    fi
}

# Show memory information with usage percentage
show_memory_info() {
    print_header "Redis Memory Information"
    if check_redis_status; then
        # Get memory info
        MEMORY_INFO=$(docker exec "$REDIS_CONTAINER" redis-cli INFO memory)
        USED_MEMORY=$(echo "$MEMORY_INFO" | grep "used_memory:" | cut -d: -f2 | tr -d '\r')
        MAX_MEMORY=$(echo "$MEMORY_INFO" | grep "maxmemory:" | cut -d: -f2 | tr -d '\r')

        echo "$MEMORY_INFO" | grep -E "(used_memory|maxmemory|mem_fragmentation|used_memory_peak)"

        # Calculate usage percentage for 2GB limit
        if [ "$MAX_MEMORY" = "2147483648" ]; then
            USED_MB=$((USED_MEMORY / 1024 / 1024))
            USAGE_PERCENT=$((USED_MEMORY * 100 / MAX_MEMORY))
            echo ""
            echo "Memory Usage: ${USED_MB}MB / 2048MB (${USAGE_PERCENT}%)"

            if [ "$USAGE_PERCENT" -gt 90 ]; then
                print_error "CRITICAL: Memory usage > 90%"
            elif [ "$USAGE_PERCENT" -gt 80 ]; then
                print_warning "WARNING: Memory usage > 80%"
            else
                print_status "Memory usage is normal"
            fi
        fi

        echo ""
        docker exec "$REDIS_CONTAINER" redis-cli INFO stats | grep -E "(keyspace_hits|keyspace_misses|expired_keys|evicted_keys)"
    else
        print_error "Redis container is not running"
        exit 1
    fi
}

# Show Redis configuration (using INFO since CONFIG is disabled)
show_config() {
    print_header "Redis Configuration Verification"
    if check_redis_status; then
        echo "=== Memory Configuration ==="
        docker exec "$REDIS_CONTAINER" redis-cli INFO memory | grep -E "(maxmemory|maxmemory_policy)"

        echo ""
        echo "=== Persistence Configuration ==="
        docker exec "$REDIS_CONTAINER" redis-cli INFO persistence | grep -E "(aof_enabled|rdb_saves)"

        echo ""
        echo "=== Server Information ==="
        docker exec "$REDIS_CONTAINER" redis-cli INFO server | grep -E "(redis_version|config_file)"

        echo ""
        echo "=== Configuration Summary ==="
        MEMORY_INFO=$(docker exec "$REDIS_CONTAINER" redis-cli INFO memory)
        MAX_MEMORY=$(echo "$MEMORY_INFO" | grep "maxmemory:" | cut -d: -f2 | tr -d '\r')
        POLICY=$(echo "$MEMORY_INFO" | grep "maxmemory_policy:" | cut -d: -f2 | tr -d '\r')

        if [ "$MAX_MEMORY" = "2147483648" ]; then
            print_status "✅ Memory limit: 2GB (correct)"
        else
            print_error "❌ Memory limit: $MAX_MEMORY (expected: 2147483648)"
        fi

        if [ "$POLICY" = "volatile-lru" ]; then
            print_status "✅ Eviction policy: volatile-lru (correct)"
        else
            print_error "❌ Eviction policy: $POLICY (expected: volatile-lru)"
        fi

        AOF_ENABLED=$(docker exec "$REDIS_CONTAINER" redis-cli INFO persistence | grep "aof_enabled:" | cut -d: -f2 | tr -d '\r')
        if [ "$AOF_ENABLED" = "0" ]; then
            print_status "✅ AOF persistence: disabled (correct)"
        else
            print_error "❌ AOF persistence: enabled (should be disabled)"
        fi

    else
        print_error "Redis container is not running"
        exit 1
    fi
}

# Show Redis statistics
show_stats() {
    print_header "Redis Statistics"
    if check_redis_status; then
        docker exec "$REDIS_CONTAINER" redis-cli INFO stats
        echo ""
        print_header "Keyspace Information"
        docker exec "$REDIS_CONTAINER" redis-cli INFO keyspace
        echo ""
        print_header "Slow Log (last 10 entries)"
        docker exec "$REDIS_CONTAINER" redis-cli SLOWLOG GET 10
    else
        print_error "Redis container is not running"
        exit 1
    fi
}

# Monitor Redis in real-time
monitor_redis() {
    print_header "Redis Real-time Monitoring (Press Ctrl+C to stop)"
    if check_redis_status; then
        docker exec -it "$REDIS_CONTAINER" redis-cli --latency-history
    else
        print_error "Redis container is not running"
        exit 1
    fi
}

# Check audio cache status
check_audio_cache() {
    print_header "Audio Cache Status"
    if check_redis_status; then
        echo "Empathy audio cache keys:"
        docker exec "$REDIS_CONTAINER" redis-cli --scan --pattern "empathy_audio:*" | wc -l
        echo ""
        echo "Workflow audio cache keys:"
        docker exec "$REDIS_CONTAINER" redis-cli --scan --pattern "workflow_audio:*" | wc -l
        echo ""
        echo "Sample cache key TTL (empathy_audio):"
        SAMPLE_KEY=$(docker exec "$REDIS_CONTAINER" redis-cli --scan --pattern "empathy_audio:*" | head -1)
        if [ ! -z "$SAMPLE_KEY" ]; then
            docker exec "$REDIS_CONTAINER" redis-cli TTL "$SAMPLE_KEY"
        else
            echo "No empathy audio cache keys found"
        fi
    else
        print_error "Redis container is not running"
        exit 1
    fi
}

# Backup Redis data (Note: Persistence is disabled in production)
backup_redis() {
    print_header "Creating Redis Backup"
    print_warning "Note: Persistence is disabled in production for performance"
    print_warning "This backup is for emergency use only and may not contain all data"

    if check_redis_status; then
        BACKUP_DIR="./redis/backups"
        mkdir -p "$BACKUP_DIR"
        TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
        BACKUP_FILE="$BACKUP_DIR/redis_backup_$TIMESTAMP.rdb"

        print_status "Creating emergency backup..."
        docker exec "$REDIS_CONTAINER" redis-cli BGSAVE

        # Wait for background save to complete
        sleep 3

        # Check if RDB file exists
        if docker exec "$REDIS_CONTAINER" test -f /data/dump.rdb; then
            # Copy the RDB file
            docker cp "$REDIS_CONTAINER:/data/dump.rdb" "$BACKUP_FILE"
            print_status "Emergency backup created: $BACKUP_FILE"
            print_warning "Remember: This backup may be empty due to disabled persistence"
        else
            print_error "No RDB file found - persistence is disabled"
        fi
    else
        print_error "Redis container is not running"
        exit 1
    fi
}

# Show help
show_help() {
    echo "Redis Production Environment Utilities"
    echo ""
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  start       Start Redis production environment"
    echo "  stop        Stop Redis production environment"
    echo "  restart     Restart Redis production environment"
    echo "  status      Check Redis status"
    echo "  connect     Connect to Redis CLI"
    echo "  memory      Show memory information"
    echo "  config      Show Redis configuration"
    echo "  stats       Show Redis statistics"
    echo "  monitor     Monitor Redis in real-time"
    echo "  audio       Check audio cache status"
    echo "  backup      Create Redis backup"
    echo "  help        Show this help message"
}

# Main script logic
case "${1:-help}" in
    start)
        start_redis
        ;;
    stop)
        stop_redis
        ;;
    restart)
        restart_redis
        ;;
    status)
        check_redis_status
        ;;
    connect)
        connect_redis
        ;;
    memory)
        show_memory_info
        ;;
    config)
        show_config
        ;;
    stats)
        show_stats
        ;;
    monitor)
        monitor_redis
        ;;
    audio)
        check_audio_cache
        ;;
    backup)
        backup_redis
        ;;
    help)
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        show_help
        exit 1
        ;;
esac
