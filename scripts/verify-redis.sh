#!/bin/bash

# Redis连接验证脚本
# 用于验证生产环境Redis配置是否正确

set -e

echo "🔍 Redis连接验证工具"
echo "===================="

# 检查Docker是否运行
if ! docker ps >/dev/null 2>&1; then
    echo "❌ Docker未运行或无权限访问"
    exit 1
fi

# 检查Redis容器是否存在
if ! docker ps | grep -q redis; then
    echo "❌ Redis容器未运行"
    echo "请先启动Redis: docker-compose -f docker-compose.prod.yml up -d redis"
    exit 1
fi

echo "✓ Redis容器正在运行"

# 测试Redis连接（无密码）
echo ""
echo "🔄 测试无密码连接（应该失败）..."
if docker exec redis redis-cli ping 2>/dev/null; then
    echo "⚠️  警告: Redis允许无密码连接，这在生产环境中不安全"
else
    echo "✓ Redis正确拒绝了无密码连接"
fi

# 测试Redis连接（有密码）
echo ""
echo "🔄 测试密码认证连接..."
if docker exec redis redis-cli -a moodplay_redis_2024_secure_password ping 2>/dev/null | grep -q PONG; then
    echo "✓ Redis密码认证成功"
else
    echo "❌ Redis密码认证失败"
    echo "请检查密码配置是否正确"
    exit 1
fi

# 检查Redis配置
echo ""
echo "🔄 检查Redis配置..."
PROTECTED_MODE=$(docker exec redis redis-cli -a moodplay_redis_2024_secure_password CONFIG GET protected-mode 2>/dev/null | tail -1)
REQUIREPASS=$(docker exec redis redis-cli -a moodplay_redis_2024_secure_password CONFIG GET requirepass 2>/dev/null | tail -1)

echo "Protected Mode: $PROTECTED_MODE"
echo "Require Pass: $([ -n "$REQUIREPASS" ] && echo "已设置" || echo "未设置")"

if [ "$PROTECTED_MODE" = "yes" ] && [ -n "$REQUIREPASS" ]; then
    echo "✓ Redis安全配置正确"
else
    echo "⚠️  Redis安全配置可能有问题"
fi

# 测试基本操作
echo ""
echo "🔄 测试Redis基本操作..."
TEST_KEY="test:verify:$(date +%s)"
TEST_VALUE="verification_test"

# SET操作
if docker exec redis redis-cli -a moodplay_redis_2024_secure_password SET "$TEST_KEY" "$TEST_VALUE" EX 10 2>/dev/null | grep -q OK; then
    echo "✓ SET操作成功"
else
    echo "❌ SET操作失败"
    exit 1
fi

# GET操作
RETRIEVED_VALUE=$(docker exec redis redis-cli -a moodplay_redis_2024_secure_password GET "$TEST_KEY" 2>/dev/null)
if [ "$RETRIEVED_VALUE" = "$TEST_VALUE" ]; then
    echo "✓ GET操作成功"
else
    echo "❌ GET操作失败"
    exit 1
fi

# 清理测试数据
docker exec redis redis-cli -a moodplay_redis_2024_secure_password DEL "$TEST_KEY" >/dev/null 2>&1

# 检查后端服务连接
echo ""
echo "🔄 检查后端服务Redis连接..."
if docker ps | grep -q moodplay-backend; then
    echo "✓ 后端服务正在运行"
    
    # 检查后端日志中的Redis相关信息
    echo "最近的Redis相关日志:"
    docker logs moodplay-backend 2>&1 | grep -i redis | tail -3 || echo "未找到Redis相关日志"
else
    echo "⚠️  后端服务未运行"
    echo "请启动后端服务: docker-compose -f docker-compose.prod.yml up -d backend"
fi

# 显示Redis信息
echo ""
echo "📊 Redis服务信息:"
docker exec redis redis-cli -a moodplay_redis_2024_secure_password INFO server 2>/dev/null | grep -E "redis_version|uptime_in_seconds" || echo "无法获取Redis信息"

echo ""
echo "🎉 Redis验证完成！"
echo ""
echo "如果所有测试都通过，Redis配置应该已经正确修复了认证问题。"
echo "如果仍有问题，请查看详细的修复指南: REDIS_AUTH_FIX.md"
