#!/usr/bin/env node

/**
 * Redis连接验证脚本
 * 用于验证Redis配置是否正确，特别是密码认证
 */

const Redis = require('ioredis');

// 从环境变量读取配置
const config = {
    host: process.env.REDIS_HOST || 'localhost',
    port: Number(process.env.REDIS_PORT) || 6379,
    connectTimeout: 3000,
    commandTimeout: 1500,
    maxRetriesPerRequest: 1,
    lazyConnect: true,
};

// 如果设置了密码，则添加密码认证
if (process.env.REDIS_PASSWORD) {
    config.password = process.env.REDIS_PASSWORD;
    console.log('✓ Redis密码认证已启用');
} else {
    console.log('ℹ Redis密码认证未设置');
}

console.log('Redis配置:', {
    host: config.host,
    port: config.port,
    hasPassword: !!config.password
});

async function verifyRedisConnection() {
    const redis = new Redis(config);
    
    try {
        console.log('\n🔄 正在连接Redis...');
        
        // 测试连接
        await redis.connect();
        console.log('✓ Redis连接成功');
        
        // 测试PING命令
        const pong = await redis.ping();
        console.log('✓ PING测试成功:', pong);
        
        // 测试基本操作
        const testKey = 'test:connection:' + Date.now();
        const testValue = 'connection_test_value';
        
        // SET操作
        await redis.set(testKey, testValue, 'EX', 10);
        console.log('✓ SET操作成功');
        
        // GET操作
        const retrievedValue = await redis.get(testKey);
        if (retrievedValue === testValue) {
            console.log('✓ GET操作成功');
        } else {
            throw new Error('GET操作返回值不匹配');
        }
        
        // 清理测试数据
        await redis.del(testKey);
        console.log('✓ DEL操作成功');
        
        // 获取Redis信息
        const info = await redis.info('server');
        const version = info.match(/redis_version:([^\r\n]+)/)?.[1];
        console.log('✓ Redis版本:', version);
        
        console.log('\n🎉 Redis连接验证完成，所有测试通过！');
        
    } catch (error) {
        console.error('\n❌ Redis连接验证失败:');
        console.error('错误类型:', error.constructor.name);
        console.error('错误信息:', error.message);
        
        if (error.message.includes('DENIED Redis is running in protected mode')) {
            console.error('\n💡 解决建议:');
            console.error('1. 检查Redis配置文件中的 protected-mode 设置');
            console.error('2. 确保设置了 requirepass 密码');
            console.error('3. 确保环境变量 REDIS_PASSWORD 已正确设置');
        } else if (error.message.includes('NOAUTH')) {
            console.error('\n💡 解决建议:');
            console.error('1. Redis需要密码认证');
            console.error('2. 请设置环境变量 REDIS_PASSWORD');
        } else if (error.message.includes('ECONNREFUSED')) {
            console.error('\n💡 解决建议:');
            console.error('1. 检查Redis服务是否正在运行');
            console.error('2. 检查主机和端口配置是否正确');
        }
        
        process.exit(1);
    } finally {
        await redis.quit();
    }
}

// 运行验证
verifyRedisConnection().catch(console.error);
