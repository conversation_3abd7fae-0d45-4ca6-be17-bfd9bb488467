#!/bin/bash

# 通用工具函数库
# 为所有脚本提供共享的功能

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

# 获取项目根目录
get_project_root() {
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[1]}")" && pwd)"
    echo "$(dirname "$script_dir")"
}

# 加载环境变量函数
load_env() {
    local project_root="${1:-$(get_project_root)}"
    local env_file="$project_root/.env"
    
    if [ -f "$env_file" ]; then
        log_info "加载环境变量从: $env_file"
        set -a  # 自动导出所有变量
        source "$env_file"
        set +a  # 关闭自动导出
        log_info "环境变量加载完成"
        return 0
    else
        log_error "未找到 .env 文件在 $env_file"
        log_error "请确保 .env 文件存在于脚本的上层目录中"
        return 1
    fi
}

# 检查必要的数据库环境变量
check_db_env() {
    local missing_vars=()
    
    [ -z "$DB_PASSWORD" ] && missing_vars+=("DB_PASSWORD")
    [ -z "$DB_USER" ] && missing_vars+=("DB_USER")
    [ -z "$DB_NAME" ] && missing_vars+=("DB_NAME")
    [ -z "$DB_HOST" ] && missing_vars+=("DB_HOST")
    
    if [ ${#missing_vars[@]} -gt 0 ]; then
        log_error "以下环境变量未设置:"
        for var in "${missing_vars[@]}"; do
            log_error "  - $var"
        done
        
        log_info "当前数据库相关环境变量:"
        echo "  DB_USER=${DB_USER:-[未设置]}"
        echo "  DB_NAME=${DB_NAME:-[未设置]}"
        echo "  DB_HOST=${DB_HOST:-[未设置]}"
        echo "  DB_PASSWORD=${DB_PASSWORD:+[已设置]}"
        
        return 1
    fi
    
    log_info "数据库环境变量检查通过"
    return 0
}

# 显示调试信息
show_debug_info() {
    log_header "调试信息"
    echo "脚本路径: ${BASH_SOURCE[1]}"
    echo "工作目录: $(pwd)"
    echo "项目根目录: $(get_project_root)"
    echo ""
    echo "数据库配置:"
    echo "  DB_USER=$DB_USER"
    echo "  DB_NAME=$DB_NAME"
    echo "  DB_HOST=$DB_HOST"
    echo "  DB_PASSWORD=${DB_PASSWORD:+[已设置，长度: ${#DB_PASSWORD}]}"
    echo ""
}
