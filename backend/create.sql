-- Drop table

-- DROP TABLE IF EXISTS users;

CREATE TABLE users (
    id INT AUTO_INCREMENT NOT NULL,
    openid VARCHAR(100) NOT NULL,
    unionid VARCHAR(100) NULL,
    phone VARCHAR(30) NULL,
    username VARCHAR(50) NULL,
    avatar_key VARCHAR(512) NULL,
    onboarding BOOLEAN DEFAULT FALSE NOT NULL,
    birth_date DATE NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL,
    PRIMARY KEY (id),
    UNIQUE KEY users_openid_key (openid),
    UNIQUE KEY users_unionid_key (unionid),
    UNIQUE KEY users_phone_key (phone)
);

-- 游戏会话表（每次play）
CREATE TABLE play_sessions (
    id INT AUTO_INCREMENT NOT NULL,
    user_id INT NOT NULL,
    input_text TEXT NOT NULL,                -- 用户输入的文本
    experience_mode VARCHAR(20) DEFAULT 'text', -- 用户选择的体验模式：text(文字), audio(声音)
    resp_quote VARCHAR(512) NULL,            -- AI响应的引言
    resp_action VARCHAR(512) NULL,           -- AI响应的行动建议
    resp_empathy TEXT NULL,                  -- AI响应的共情内容
    resp_workflow TEXT NULL,                 -- AI响应的工作流程
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(20) DEFAULT 'active',     -- 会话状态：active, completed
    PRIMARY KEY (id),
    CONSTRAINT fk_user FOREIGN KEY (user_id) REFERENCES users(id)
);
CREATE INDEX idx_play_sessions_user_id ON play_sessions(user_id);

-- AI识别的情绪（可能多个）
CREATE TABLE detected_emotions (
    id INT AUTO_INCREMENT NOT NULL,
    play_session_id INT NOT NULL,
    user_id INT NOT NULL,
    emotion_key VARCHAR(10) NOT NULL,
    emotion_description VARCHAR(30),
    symbol SMALLINT DEFAULT 0 NOT NULL,
    is_selected BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    CONSTRAINT fk_play_session_emotions_detected FOREIGN KEY (play_session_id) REFERENCES play_sessions(id),
    CONSTRAINT fk_detected_emotions_user FOREIGN KEY (user_id) REFERENCES users(id)
);
CREATE INDEX idx_detected_emotions_play_session_id ON detected_emotions(play_session_id);
CREATE INDEX idx_detected_emotions_user_id ON detected_emotions(user_id);
CREATE INDEX idx_detected_emotions_selected ON detected_emotions(is_selected);

-- AI建议的目标（可能多个）
CREATE TABLE suggested_goals (
    id INT AUTO_INCREMENT NOT NULL,
    play_session_id INT NOT NULL,
    user_id INT NOT NULL,
    goal_key VARCHAR(20) NOT NULL,
    goal_description VARCHAR(30),
    goal_long_description VARCHAR(100),
    goal_image_key VARCHAR(512) NULL,
    is_selected BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    CONSTRAINT fk_play_session_goals_suggested FOREIGN KEY (play_session_id) REFERENCES play_sessions(id),
    CONSTRAINT fk_suggested_goals_user FOREIGN KEY (user_id) REFERENCES users(id)
);
CREATE INDEX idx_suggested_goals_play_session_id ON suggested_goals(play_session_id);
CREATE INDEX idx_suggested_goals_user_id ON suggested_goals(user_id);
CREATE INDEX idx_suggested_goals_selected ON suggested_goals(is_selected);

-- 用户全局配置表
CREATE TABLE user_settings (
    id INT AUTO_INCREMENT NOT NULL,
    user_id INT NOT NULL,
    voice_name VARCHAR(32) NULL,
    language VARCHAR(20) DEFAULT 'zh-CN',    -- 语言设置
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL,
    PRIMARY KEY (id),
    UNIQUE KEY user_settings_user_id_key (user_id),
    CONSTRAINT fk_user_settings FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 邀请关系表
CREATE TABLE invitations (
    id INT AUTO_INCREMENT NOT NULL,
    inviter_id INT NOT NULL,                 -- 邀请者用户ID
    invitee_id INT NOT NULL,                 -- 被邀请者用户ID
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL,
    PRIMARY KEY (id),
    UNIQUE KEY uq_inviter_invitee (inviter_id, invitee_id), -- 防止重复邀请
    CONSTRAINT fk_inviter FOREIGN KEY (inviter_id) REFERENCES users(id),
    CONSTRAINT fk_invitee FOREIGN KEY (invitee_id) REFERENCES users(id)
);
CREATE INDEX idx_invitations_inviter_id ON invitations(inviter_id);
CREATE INDEX idx_invitations_invitee_id ON invitations(invitee_id);

-- 会员方案表（只保留需要购买的套餐）
CREATE TABLE membership_plans (
    id INT AUTO_INCREMENT NOT NULL,
    name VARCHAR(100) NOT NULL,                      -- 方案名称 (例如: "月度会员", "终身会员", "连续包月会员")
    plan_key VARCHAR(50) NOT NULL UNIQUE,            -- 方案唯一标识 (例如: "premium_monthly", "premium_lifetime", "premium_monthly_auto_renew")
    duration_days INT NULL,                          -- 有效天数（对于终身会员可以为NULL）
    price DECIMAL(10, 2) NOT NULL,                   -- 价格
    renewal_type ENUM('NONE', 'AUTO_RENEW', 'LIFETIME') NOT NULL DEFAULT 'NONE', -- 续订类型
    description TEXT NULL,                           -- 方案描述
    is_active BOOLEAN DEFAULT TRUE,                  -- 方案是否启用
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL,
    PRIMARY KEY (id)
);
CREATE INDEX idx_membership_plans_is_active ON membership_plans(is_active);
CREATE INDEX idx_membership_plans_renewal_type ON membership_plans(renewal_type);

-- 会员订单表
CREATE TABLE membership_orders (
    id INT AUTO_INCREMENT NOT NULL,
    order_no VARCHAR(100) NOT NULL,                -- 订单号
    user_id INT NOT NULL,
    plan_id INT NOT NULL,                            -- 购买的会员方案ID
    amount DECIMAL(10, 2) NOT NULL,                  -- 支付金额
    currency VARCHAR(10) DEFAULT 'CNY',              -- 货币单位
    status ENUM('PENDING', 'PAID', 'FAILED', 'REFUNDED', 'CLOSED') DEFAULT 'PENDING', -- 订单状态
    payment_method VARCHAR(50) NULL,                 -- 支付方式 (例如: "WECHAT_PAY", "ALIPAY")
    transaction_id VARCHAR(100) NULL,                -- 支付平台交易号
    paid_at DATETIME NULL,                           -- 支付完成时间
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT fk_membership_orders_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_membership_orders_plan FOREIGN KEY (plan_id) REFERENCES membership_plans(id)
);
CREATE INDEX idx_membership_orders_user_id ON membership_orders(user_id);
CREATE INDEX idx_membership_orders_plan_id ON membership_orders(plan_id);
CREATE INDEX idx_membership_orders_status ON membership_orders(status);
CREATE UNIQUE INDEX uq_membership_orders_transaction_id ON membership_orders(transaction_id);

-- 奖励类型配置表
CREATE TABLE reward_types (
    id INT AUTO_INCREMENT NOT NULL,
    type_key VARCHAR(50) NOT NULL UNIQUE,           -- 奖励类型唯一标识 (例如: "new_user", "invite_reward", "achievement")
    name VARCHAR(100) NOT NULL,                     -- 奖励类型名称 (例如: "新用户奖励", "邀请奖励", "达成成就")
    description TEXT NULL,                          -- 奖励类型描述
    reward_days INT NOT NULL DEFAULT 0,             -- 默认奖励天数
    is_repeatable BOOLEAN DEFAULT FALSE,            -- 是否可重复获得
    is_active BOOLEAN DEFAULT TRUE,                 -- 奖励类型是否启用
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL,
    PRIMARY KEY (id)
);
CREATE INDEX idx_reward_types_type_key ON reward_types(type_key);
CREATE INDEX idx_reward_types_is_active ON reward_types(is_active);
CREATE INDEX idx_reward_types_is_repeatable ON reward_types(is_repeatable);

-- 用户奖励记录表（保持不变）
CREATE TABLE user_rewards (
    id INT AUTO_INCREMENT NOT NULL,
    user_id INT NOT NULL,                           -- 获得奖励的用户ID
    reward_type_id INT NOT NULL,                    -- 奖励类型ID
    reward_days INT NOT NULL,                       -- 实际奖励的天数
    status ENUM('GRANTED', 'MERGED') DEFAULT 'GRANTED', -- 奖励状态，默认为已发放
    expire_date DATETIME NOT NULL,                  -- 奖励天数的到期时间
    notes TEXT NULL,                                -- 备注信息
    merge_at DATETIME NULL,                         -- 合并时间
    merge_reason TEXT NULL,                          -- 合并原因，记录合并到哪个套餐及相关信息
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT fk_user_rewards_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_user_rewards_type FOREIGN KEY (reward_type_id) REFERENCES reward_types(id)
);
CREATE INDEX idx_user_rewards_user_id ON user_rewards(user_id);
CREATE INDEX idx_user_rewards_type_id ON user_rewards(reward_type_id);
CREATE INDEX idx_user_rewards_user_status ON user_rewards(user_id, status);
CREATE INDEX idx_user_rewards_user_expire ON user_rewards(user_id, expire_date);
CREATE INDEX idx_user_rewards_user_type ON user_rewards(user_id, reward_type_id);

-- 用户反馈表
CREATE TABLE user_feedbacks (
    id INT AUTO_INCREMENT NOT NULL,
    user_id INT NOT NULL,                           -- 反馈用户ID
    rating TINYINT NOT NULL CHECK (rating >= 1 AND rating <= 5), -- 5星评分，1-5分
    content TEXT NULL,                              -- 反馈内容
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT fk_user_feedbacks_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
CREATE INDEX idx_user_feedbacks_user_id ON user_feedbacks(user_id);
CREATE INDEX idx_user_feedbacks_rating ON user_feedbacks(rating);
CREATE INDEX idx_user_feedbacks_created_at ON user_feedbacks(created_at);

-- 用户占卜记录表
CREATE TABLE user_divinations (
    id INT AUTO_INCREMENT NOT NULL,
    user_id INT NOT NULL,                           -- 占卜用户ID
    input_text TEXT NOT NULL,                       -- 用户输入的占卜问题或内容
    ai_response TEXT NOT NULL,                      -- AI返回的占卜结果    
    success BOOLEAN DEFAULT TRUE NOT NULL,                   -- 占卜是否成功
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT fk_user_divinations_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
CREATE INDEX idx_user_divinations_user_date ON user_divinations(user_id, created_at);

-- 用户购买的会员计划记录表
-- 记录用户购买的所有付费会员计划及其有效期
CREATE TABLE user_paid_memberships (
    id INT AUTO_INCREMENT NOT NULL,
    user_id INT NOT NULL,
    plan_id INT NOT NULL,                            -- 购买的会员方案ID
    start_date DATETIME NOT NULL,                    -- 会员开始时间
    expire_date DATETIME NOT NULL,                   -- 会员到期时间
    is_renewal_active BOOLEAN NOT NULL DEFAULT TRUE, -- 是否启用自动续费（仅对AUTO_RENEW类型有效）
    status ENUM('ACTIVE', 'EXPIRED', 'CANCELLED') DEFAULT 'ACTIVE', -- 会员状态
    order_id INT NULL,                               -- 关联的订单ID
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT fk_user_paid_memberships_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_user_paid_memberships_plan FOREIGN KEY (plan_id) REFERENCES membership_plans(id),
    CONSTRAINT fk_user_paid_memberships_order FOREIGN KEY (order_id) REFERENCES membership_orders(id)
);
CREATE INDEX idx_user_paid_memberships_user_id ON user_paid_memberships(user_id);
CREATE INDEX idx_user_paid_memberships_expire_date ON user_paid_memberships(expire_date);
CREATE INDEX idx_user_paid_memberships_status ON user_paid_memberships(status);
CREATE INDEX idx_user_paid_memberships_user_status ON user_paid_memberships(user_id, status);
