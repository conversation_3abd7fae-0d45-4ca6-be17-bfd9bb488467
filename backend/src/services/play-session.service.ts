import { AppError } from '../middlewares/error.middleware';
import { PartnerAiService } from './partner-ai.service';
import { PlaySession, DetectedEmotion, SuggestedGoal } from '../models';
import { SubmitPlaySessionResult, PlaySessionDetail, PlayInfo, DetectedEmotionExtra } from '../types/play-session.type';
import { PlaySessionStatus } from '../enums';
import { TransactionService } from './transaction.service';
import {
    GenerateEmpathyParams,
    ProcessWorkflowParams,
    ProcessedGenerateEmpathyResult,
    ProcessedProcessWorkflowResult
} from '../types/partner-ai.types';
import { OssService } from './oss.service';
import logger from '../utils/logger';
import { EndGoalConfig, PlayConfig } from '../types/config.type';
import { RedisService } from './redis.service';
import sequelize from '../config/sequelize';
import { Op } from 'sequelize';
import { UserSettingService } from './user-setting.service';
export class PlaySessionService {

    static async get(playSessionId: number): Promise<PlaySessionDetail | null> {
        const playSessionModel = await PlaySession.findByPk(playSessionId, {
            include: [
                {
                    model: DetectedEmotion,
                    as: 'detectedEmotions'
                },
                {
                    model: SuggestedGoal,
                    as: 'suggestedGoals'
                }
            ],
            attributes: {
                exclude: ['respQuote', 'respAction', 'respEmpathy']
            }
        });
        if (!playSessionModel) {
            throw new AppError('play session not found', 404);
        }
        const playSessionDetail = playSessionModel.toJSON() as PlaySessionDetail;

        // 处理 DetectedEmotionExtra 中的 icon 和 iconSelected
        if (playSessionDetail.detectedEmotions && playSessionDetail.detectedEmotions.length > 0) {
            playSessionDetail.detectedEmotions = await this.processDetectedEmotions(playSessionDetail.detectedEmotions, playSessionId);
        }

        return playSessionDetail;
    }

    private static async processDetectedEmotions(detectedEmotions: DetectedEmotion[], playSessionId: number): Promise<DetectedEmotionExtra[]> {
        const symbolToCategoryMap = {
            4: 'high-un-pleasent',
            3: 'low-un-pleasent',
            2: 'low-pleasent',
            1: 'high-pleasent'
        };

        // 跟踪每个category已使用的icon编号，确保不重复
        const usedIcons = new Map<string, Set<number>>();

        return await Promise.all(
            detectedEmotions.map(async (emotion) => {
                const category = symbolToCategoryMap[emotion.symbol as keyof typeof symbolToCategoryMap];
                if (!category) {
                    logger.warn(`Unknown symbol: ${emotion.symbol} for emotion in session ${playSessionId}`);
                    // 如果 symbol 不匹配，使用默认值
                    return {
                        ...emotion,
                        icon: '',
                        iconSelected: ''
                    } as DetectedEmotionExtra;
                }

                // 获取或创建该category的已使用icon集合
                if (!usedIcons.has(category)) {
                    usedIcons.set(category, new Set<number>());
                }
                const usedSet = usedIcons.get(category)!;

                // 生成可用的icon编号列表（1-6）
                const availableNames = [1, 2, 3, 4, 5, 6].filter(num => !usedSet.has(num));

                let name: number;
                if (availableNames.length > 0) {
                    // 从可用的编号中随机选择
                    const randomIndex = Math.floor(Math.random() * availableNames.length);
                    name = availableNames[randomIndex];
                    usedSet.add(name); // 标记为已使用
                    logger.debug(`Selected unique icon ${name} for category ${category} in session ${playSessionId}`);
                } else {
                    // 如果所有编号都已使用，则随机选择一个（允许重复）
                    name = Math.floor(Math.random() * 6) + 1;
                    logger.info(`All icons used for category ${category}, allowing duplicate icon ${name} in session ${playSessionId}`);
                }

                // 构造 OSS 路径
                const iconPath = `assets/pages/check-in/emotion-icon/${category}/${name}.svg`;
                const iconSelectedPath = `assets/pages/check-in/emotion-icon/${category}/${name}-selected.svg`;

                try {
                    // 生成签名 URL
                    const [iconUrl, iconSelectedUrl] = await Promise.all([
                        OssService.generateResourceUrl(iconPath),
                        OssService.generateResourceUrl(iconSelectedPath)
                    ]);

                    return {
                        ...emotion,
                        icon: iconUrl,
                        iconSelected: iconSelectedUrl
                    } as DetectedEmotionExtra;
                } catch (error) {
                    logger.error(`Error generating signed URLs for emotion icons in session ${playSessionId}:`, error);
                    return {
                        ...emotion,
                        icon: '',
                        iconSelected: ''
                    } as DetectedEmotionExtra;
                }
            })
        );
    }

    static async submit(userId: number, text: string): Promise<SubmitPlaySessionResult> {
        return await TransactionService.executeInTransaction(async (transaction) => {
            try {
                // 1. 并行执行 AI 分析、目标设定和 PlaySession 创建
                const [analyzeResult, setGoalsResult, playSession] = await Promise.all([
                    PartnerAiService.analyzeEmotion(text).then(result => {
                        return result;
                    }),
                    PartnerAiService.setGoals(text).then(result => {
                        return result;
                    }),
                    PlaySession.create({
                        userId: userId,
                        inputText: text,
                        status: PlaySessionStatus.ACTIVE
                    }, { transaction }).then(result => {
                        return result;
                    })
                ]);
                const emotionsToCreate = analyzeResult.emotions.map(emotion => ({
                    playSessionId: playSession.id,
                    emotionKey: emotion.emotion,
                    emotionDescription: emotion.explanation,
                    symbol: emotion.symbol,
                    userId

                }));

                const goalsToCreate = setGoalsResult.goals.map(goal => ({
                    playSessionId: playSession.id,
                    goalKey: goal.channel,
                    goalDescription: goal.short,
                    goalLongDescription: goal.long,
                    userId,
                }));

                // 3. 并行执行批量创建情绪和目标
                await Promise.all([
                    DetectedEmotion.bulkCreate(emotionsToCreate, { transaction }).then(() => {
                    }),
                    SuggestedGoal.bulkCreate(goalsToCreate, { transaction }).then(() => {
                    })
                ]);
                return {
                    playSessionId: playSession.id,
                };
            } catch (error) {
                logger.error("submit play session error: ", error);
                // 可以考虑根据 error 类型细化错误消息
                if (error instanceof AppError) {
                    throw error;
                }
                // 检查是否是 Sequelize 相关的错误或其他已知错误类型
                throw new AppError('ooops 系统好像有点问题！', 500);
            }
        });
    }

    /**
     * 统计用户完成的目标数量
     * @param userId 用户ID
     * @param currentAchievedGoalKey 当前达成的目标键
     * @returns 包含6种目标类型及其完成数量的数组
     */
    static async getFinishedGoalsCount(userId: number, currentAchievedGoalKey: string): Promise<Array<{ goalKey: string; count: number; firstAchieved: boolean, currentAchieved: boolean }>> {
        try {
            // 定义所有可能的目标类型
            const allGoalKeys = ['松弛时刻', '释怀时刻', '共情时刻', '稳定时刻', '行动时刻', '灵光时刻'];

            // 查询用户所有已完成的会话中选中的目标
            const goalCounts = await SuggestedGoal.findAll({
                attributes: [
                    'goalKey',
                    [sequelize.fn('COUNT', sequelize.col('SuggestedGoal.id')), 'count']
                ],
                include: [{
                    model: PlaySession,
                    where: {
                        userId: userId,
                        status: {
                            [Op.in]: [PlaySessionStatus.FINISH_MOODPLAY, PlaySessionStatus.COMPLETED]
                        }
                    },
                    attributes: [] // 不需要返回 PlaySession 的字段
                }],
                where: {
                    isSelected: true
                },
                group: ['goalKey'],
                raw: true
            });

            // 将查询结果转换为 Map 以便快速查找
            const countMap = new Map<string, number>();
            goalCounts.forEach((item: any) => {
                countMap.set(item.goalKey, parseInt(item.count) || 0);
            });

            // 构建最终结果，确保包含所有6种目标类型
            const finishedGoals = allGoalKeys.map(goalKey => {
                const count = countMap.get(goalKey) || 0;
                return {
                    goalKey,
                    count,
                    firstAchieved: count === 1 && goalKey === currentAchievedGoalKey,
                    currentAchieved: goalKey === currentAchievedGoalKey
                };
            });

            logger.info(`Finished goals count for user ${userId}:`, finishedGoals);
            return finishedGoals;

        } catch (error) {
            logger.error(`Error getting finished goals count for user ${userId}:`, error);
            throw new AppError('获取完成目标统计失败', 500);
        }
    }

    /**
     * 更新播放会话状态
     * @param userId 用户ID
     * @param playSessionId 播放会话ID
     * @param newStatus 新状态
     * @returns 更新后的播放会话
     */
    static async updateStatus(userId: number, playSessionId: number, newStatus: PlaySessionStatus): Promise<{ success: boolean, message: string }> {
        try {
            // 首先检查会话是否存在且用户有权限
            const playSession = await PlaySession.findByPk(playSessionId);

            if (!playSession) {
                throw new AppError('播放会话不存在', 404);
            }

            if (playSession.userId !== userId) {
                throw new AppError('无权限访问此会话', 403);
            }

            const currentStatus = playSession.status as PlaySessionStatus;

            // 如果状态相同，直接返回成功
            if (currentStatus === newStatus) {
                return {
                    success: true,
                    message: '状态无需更新'
                };
            }

            // 定义每个目标状态所需的前置状态
            const requiredPreviousStatus: Record<PlaySessionStatus, PlaySessionStatus | null> = {
                [PlaySessionStatus.ACTIVE]: null, // active 是初始状态，不能从其他状态转换而来
                [PlaySessionStatus.FINISH_MOODPLAY]: PlaySessionStatus.ACTIVE,
                [PlaySessionStatus.COMPLETED]: PlaySessionStatus.FINISH_MOODPLAY
            };

            const expectedPreviousStatus = requiredPreviousStatus[newStatus];

            // 如果目标状态不允许转换（如转换到 ACTIVE），直接拒绝
            if (expectedPreviousStatus === null) {
                throw new AppError(`不能转换到状态: ${newStatus}`, 400);
            }

            // 使用数据库层面的原子更新，确保只有当前状态正确时才能更新
            const [affectedRows] = await PlaySession.update(
                { status: newStatus },
                {
                    where: {
                        id: playSessionId,
                        userId: userId,
                        status: expectedPreviousStatus
                    }
                }
            );

            // 如果没有行被更新，说明状态转换不合法
            if (affectedRows === 0) {
                throw new AppError(`无效的状态转换：当前状态不是 ${expectedPreviousStatus}，无法转换到 ${newStatus}`, 400);
            }

            logger.info(`PlaySession ${playSessionId} status updated from ${expectedPreviousStatus} to ${newStatus} by user ${userId}`);

            return {
                success: true,
                message: '状态更新成功'
            };

        } catch (error) {
            logger.error(`Error updating PlaySession status for playSessionId ${playSessionId}:`, error);
            if (error instanceof AppError) {
                throw error;
            }
            throw new AppError('状态更新失败', 500);
        }
    }

    /**
     * 获取用户的语音ID，包含缓存优化
     * @param userId 用户ID
     * @param playSessionId 可选的播放会话ID，用于日志记录
     * @returns 用户的语音ID
     */
    private static async getUserVoiceId(userId: number, playSessionId?: number): Promise<string> {
        // 获取用户设置
        const userSetting = await UserSettingService.getSetting(userId);
        const voiceName = userSetting?.voiceName;

        // 获取语音列表（带缓存）
        const voiceList = await this.getCachedVoiceList();

        let voiceId = '';
        if (!voiceName) {
            voiceId = voiceList[0]?.voice_id || '';
        } else {
            const voice = voiceList.find(v => v.name === voiceName);
            voiceId = voice?.voice_id || '';
        }

        if (!voiceId) {
            logger.error(`Voice not found for user ${userId}, voiceName: ${voiceName}`);
            if (playSessionId) {
                logger.warn(`Using default voice for generation for playSessionId ${playSessionId}`);
            }
            // 尝试使用默认语音
            voiceId = voiceList[0]?.voice_id || '';
            if (!voiceId) {
                throw new AppError('No voice available', 500);
            }
        }

        return voiceId;
    }

    /**
     * 获取缓存的语音列表，缓存时间为5分钟
     * @returns 语音列表
     */
    private static async getCachedVoiceList(): Promise<any[]> {
        const cacheKey = 'voice_list';

        try {
            const cachedVoiceList = await RedisService.get(cacheKey);

            if (cachedVoiceList) {
                try {
                    const parsedList = JSON.parse(cachedVoiceList);
                    logger.debug('Using cached voice list');
                    return parsedList;
                } catch (error) {
                    logger.warn('Failed to parse cached voice list, fetching fresh data', error);
                }
            }
        } catch (error) {
            logger.warn('Failed to get cached voice list from Redis', error);
        }

        // 获取新的语音列表
        logger.debug('Fetching fresh voice list from AI service');
        const voiceList = await PartnerAiService.getVoiceList();

        // 缓存5分钟 (300秒)
        try {
            await RedisService.set(cacheKey, JSON.stringify(voiceList), 300);
            logger.debug('Voice list cached successfully');
        } catch (error) {
            logger.warn('Failed to cache voice list', error);
        }

        return voiceList;
    }

    // player

    static async generateEmpathyResponse(userId: number, playSessionId: number): Promise<boolean> {
        try {
            // 生成版本戳并设置为当前最新版本
            const versionTimestamp = Date.now();
            const versionKey = `empathy_version:${playSessionId}`;
            const versionSetSuccess = await RedisService.set(versionKey, versionTimestamp.toString(), 300); // 5分钟过期
            if (!versionSetSuccess) {
                logger.error(`Failed to set version key ${versionKey} for playSessionId: ${playSessionId}`);
                throw new AppError('请稍后重试', 500);
            }
            logger.info(`Generated new version ${versionTimestamp} for playSessionId: ${playSessionId}`);

            const playSession = await PlaySessionService.get(playSessionId);
            if (!playSession) {
                throw new AppError('Play session not found', 404);
            }
            if (playSession.userId !== userId) {
                throw new AppError('Play session not found or access denied', 404);
            }

            // 先获取当前选择的目标，用于缓存验证
            const selectedGoals = playSession.suggestedGoals?.filter(g => g.isSelected);
            if (!selectedGoals || selectedGoals.length === 0) {
                throw new AppError('No selected goal found for this session. Please select a goal first.', 400);
            }
            const currentGoal = selectedGoals[0];
            if (!currentGoal) {
                throw new AppError('No primary goal found for this session. Please select a goal first.', 400);
            }

            // 异步触发 workflow 生成，不等待结果
            PlaySessionService.generateWorkflowResponse(userId, playSessionId).catch((error: any) => {
                logger.error(`Async workflow generation failed for playSessionId ${playSessionId}:`, error);
            });

            // 检查Redis中是否已有缓存的音频数据
            const cacheKey = `empathy_audio:${playSessionId}`;
            const cachedResult = await RedisService.get(cacheKey);

            if (cachedResult) {
                try {
                    const existingResponse: ProcessedGenerateEmpathyResult & { goalKey?: string } = JSON.parse(cachedResult);

                    // 检查缓存中的goal是否与当前选择的goal一致
                    if (existingResponse.goalKey && existingResponse.goalKey === currentGoal.goalKey) {
                        logger.info(`Using cached empathy response from Redis for playSessionId: ${playSessionId}, goal: ${currentGoal.goalKey}, version: ${versionTimestamp}`);

                        // 缓存命中且goal一致，更新版本戳到当前版本（表示这个缓存结果对应当前版本）
                        await RedisService.set(versionKey, versionTimestamp.toString(), 300);

                        // 缓存命中且goal一致，直接返回
                        return true;
                    } else {
                        logger.info(`Goal changed from ${existingResponse.goalKey} to ${currentGoal.goalKey} for playSessionId: ${playSessionId}, clearing old cache`);

                        // Goal不一致，删除旧缓存
                        await RedisService.del(cacheKey);
                    }
                } catch (parseError) {
                    logger.warn(`Failed to parse cached empathy response for playSessionId: ${playSessionId}, regenerating...`);
                    // 删除损坏的缓存
                    await RedisService.del(cacheKey);
                }
            }

            const detectedEmotions = playSession.detectedEmotions;
            if (!detectedEmotions || detectedEmotions.length === 0) {
                throw new AppError('No detected emotion found for this session. Please select an emotion first.', 400);
            }
            const selectedEmotions = detectedEmotions.filter(e => e.isSelected);
            if (!selectedEmotions || selectedEmotions.length === 0) {
                throw new AppError('No selected emotion found for this session. Please select an emotion first.', 400);
            }
            // 使用之前已获取的currentGoal，避免重复声明
            const primaryGoal = currentGoal;
            const primaryEmotion = selectedEmotions[0];
            if (!primaryEmotion) {
                throw new AppError('No primary emotion found for this session. Please select an emotion first.', 400);
            }
            const unselectedEmotions = detectedEmotions.filter(e => !e.isSelected);

            // 构建 emotion 参数字符串 (选中的情绪)
            const emotionStrings = selectedEmotions.map(e => {
                return e.emotionKey;
            });
            const emotionParam = emotionStrings.join(',');

            // 构建 no_emotion 参数字符串 (未选中的情绪)
            const noEmotionStrings = unselectedEmotions.map(e => {
                return e.emotionKey;
            });
            const noEmotionParam = noEmotionStrings.join(',');

            const voiceId = await this.getUserVoiceId(userId);

            // 固定 output_style 和 generate_audio
            const finalOutputStyle = 'audio';
            const generateAudioFlag = 'true';

            const aiParams: GenerateEmpathyParams = {
                text: playSession.inputText,
                goal: primaryGoal.goalKey, // 使用第一个选定目标的 goalKey
                emotion: emotionParam,    // 使用拼接后的情绪字符串
                no_emotion: noEmotionParam, // 使用拼接后的未选择情绪字符串
                voice_id: voiceId,        // 用户选择的语音ID
                output_style: finalOutputStyle,
                generate_audio: generateAudioFlag,
            };
            logger.info(`generateEmpathyResponse aiParams ${JSON.stringify(aiParams)}`);

            // 调用AI服务获取音频数据
            const aiCallStart = Date.now();
            const aiResult = await PartnerAiService.generateEmpathy(aiParams);
            const aiCallDuration = Date.now() - aiCallStart;

            if (!aiResult.audio) {
                throw new AppError('Audio base64 is missing in AI response', 500);
            }

            // 在保存结果前检查版本戳，确保当前版本仍然是最新的
            const currentVersion = await RedisService.get(versionKey);
            if (!currentVersion || parseInt(currentVersion) !== versionTimestamp) {
                logger.info(`Version outdated for playSessionId: ${playSessionId}, current: ${currentVersion}, expected: ${versionTimestamp}, skipping save`);
                return false; // 版本已过期，不保存结果
            }

            // 构建结果对象，包含base64音频数据和goalKey用于缓存验证
            const processedResult: ProcessedGenerateEmpathyResult & { goalKey: string } = {
                text: aiResult.text,
                tts_text: aiResult.tts_text,
                audio_url: `data:audio/wav;base64,${aiResult.audio}`, // 使用data URL格式
                generated_at: new Date().toISOString(),
                ai_call_duration: aiCallDuration,
                goalKey: primaryGoal.goalKey // 添加goalKey用于下次缓存验证
            };

            // 再次检查版本戳（双重检查，防止在构建结果时版本被更新）
            const finalVersion = await RedisService.get(versionKey);
            if (!finalVersion || parseInt(finalVersion) !== versionTimestamp) {
                logger.info(`Version outdated during save for playSessionId: ${playSessionId}, current: ${finalVersion}, expected: ${versionTimestamp}, skipping save`);
                return false; // 版本已过期，不保存结果
            }

            // 缓存到Redis，TTL设置为15分钟（900秒）
            const cacheValue = JSON.stringify(processedResult);
            await RedisService.set(cacheKey, cacheValue, 900);
            logger.info(`Empathy response cached to Redis for playSessionId: ${playSessionId}, version: ${versionTimestamp}, TTL: 15 minutes`);

            // 更新数据库，存储不含base64的元数据版本
            const dbResult = {
                ...processedResult,
                audio_url: `redis:${cacheKey}` // 标记数据在Redis中
            };
            await PlaySessionService.updatePlaySession(playSessionId, 'respEmpathy', JSON.stringify(dbResult));

            return true;
        } catch (error) {
            logger.error(`generateEmpathyResponse error ${error}`);
            return false;
        }

    }

    static async updatePlaySession(playSessionId: number, key: string, value: string): Promise<void> {
        await PlaySession.update({ [key]: value }, { where: { id: playSessionId } });
    }

    /**
     * 清除指定会话的音频缓存数据
     * 包括empathy和workflow的音频数据及版本控制key
     * @param playSessionId 会话ID
     */
    static async clearAudioCache(playSessionId: number): Promise<void> {
        try {
            const keysToDelete = [
                `empathy_audio:${playSessionId}`,      // empathy音频数据
                `empathy_version:${playSessionId}`,    // empathy版本控制
                `workflow_audio:${playSessionId}`,     // workflow音频数据
                `workflow_version:${playSessionId}`    // workflow版本控制
            ];

            // 批量删除所有相关的Redis key
            const deletePromises = keysToDelete.map(key => RedisService.del(key));
            const results = await Promise.all(deletePromises);

            const deletedCount = results.reduce((sum, result) => sum + result, 0);
            logger.info(`Cleared audio cache for playSessionId: ${playSessionId}, deleted ${deletedCount} keys`);

        } catch (error) {
            logger.error(`Failed to clear audio cache for playSessionId: ${playSessionId}`, error);
            // 不抛出错误，避免影响主流程
        }
    }

    static async getPlayInfo(userId: number, playSessionId: number): Promise<PlayInfo> {
        const playSession = await PlaySession.findByPk(playSessionId);
        if (!playSession || playSession.userId !== userId) {
            throw new AppError('Play session not found or access denied', 404);
        }

        // 查询用户选择的情绪 (isSelected: true)
        const selectedEmotions = await DetectedEmotion.findAll({
            where: { playSessionId: playSessionId, isSelected: true }
        });
        if (!selectedEmotions || selectedEmotions.length === 0) {
            throw new AppError('No selected emotion found for this session. Please select an emotion first.', 400);
        }

        // 查询用户选择的目标 (isSelected: true)
        const selectedGoals = await SuggestedGoal.findAll({
            where: { playSessionId: playSessionId, isSelected: true }
        });
        if (!selectedGoals || selectedGoals.length === 0) {
            throw new AppError('No selected goal found for this session. Please select a goal first.', 400);
        }
        // 根据需求，我们只取第一个选中的目标
        const primaryGoal = selectedGoals[0];

        let tagIcon = '';
        const endGoalConfig = await OssService.getObjectJsonContentWithCache<EndGoalConfig>('end-goal');
        const theGoalConfig = endGoalConfig.goalConfigs.filter(goalConfig => goalConfig.goalKey === primaryGoal.goalKey)[0];
        if (!theGoalConfig) {
            throw new AppError('Goal config not found', 400);
        }
        if (theGoalConfig) {
            if (!theGoalConfig.tagIcon) {
                throw new AppError('Tag icon not found', 400);
            }
            tagIcon = OssService.generateResourceUrl(theGoalConfig.tagIcon);
        }

        let empathyResult: ProcessedGenerateEmpathyResult = JSON.parse(playSession.respEmpathy || '{}');
        if (!empathyResult) {
            throw new AppError('Empathy response not found', 400);
        }

        // 检查是否存在音频缓存，并验证缓存内容与当前选择的目标是否一致
        let audioUrl = '';
        if (empathyResult.audio_url?.startsWith('redis:')) {
            const cacheKey = empathyResult.audio_url.replace('redis:', '');
            logger.info(`getPlayInfo cacheKey ${cacheKey}`);
            const cachedResult = await RedisService.get(cacheKey);

            if (cachedResult) {
                try {
                    // 解析缓存内容，检查goalKey是否一致
                    const cachedAudioResult = JSON.parse(cachedResult);
                    const cachedGoalKey = cachedAudioResult.goalKey;

                    if (cachedGoalKey && cachedGoalKey === primaryGoal.goalKey) {
                        // 缓存中的goalKey与当前选择的goalKey一致，返回音频URL
                        audioUrl = `/play-session/${playSessionId}/empathy-audio`;
                        logger.info(`Audio cache available and goal matches for playSessionId: ${playSessionId}, goalKey: ${primaryGoal.goalKey}`);
                    } else {
                        // 缓存中的goalKey与当前选择的goalKey不一致，返回空URL
                        audioUrl = '';
                        logger.info(`Audio cache exists but goal mismatch for playSessionId: ${playSessionId}, cached: ${cachedGoalKey}, current: ${primaryGoal.goalKey}`);
                    }
                } catch (parseError) {
                    logger.warn(`Failed to parse cached audio result for playSessionId: ${playSessionId}, returning empty audioUrl`);
                    audioUrl = '';
                }
            } else {
                logger.warn(`Empathy audio cache expired for playSessionId: ${playSessionId}`);
                // throw new AppError('Audio cache has expired, please regenerate empathy response', 410);
                audioUrl = 'invalid';
            }
        } else {
            throw new AppError('Empathy audio not available', 400);
        }
        const playConfig = await OssService.getObjectJsonContentWithCache<PlayConfig>('play');
        if (!playConfig || !playConfig.seamAudioUrl || !playConfig.endAudioUrl) {
            throw new AppError('Play config not found', 400);
        }
        const seamAudioUrl = OssService.generateResourceUrl(playConfig.seamAudioUrl);
        const endAudioUrl = OssService.generateResourceUrl(playConfig.endAudioUrl);
        const pauseButtonIcon = OssService.generateResourceUrl(playConfig.pauseButtonIcon);
        const playButtonIcon = OssService.generateResourceUrl(playConfig.playButtonIcon);
        const glitchImage = OssService.generateResourceUrl(playConfig.glitchImage);
        if (!primaryGoal.goalImageKey) {
            throw new AppError('Goal image key not found', 400);
        }
        const goalImage = OssService.generateResourceUrl(primaryGoal.goalImageKey);
        return {
            goalKey: primaryGoal.goalKey,
            tagIcon,
            goalDescription: primaryGoal.goalDescription || '',
            preAudioUrl: audioUrl,
            seamAudioUrl: seamAudioUrl,
            endAudioUrl: endAudioUrl,
            mainColor: theGoalConfig.mainColor,
            glitchImage,
            goalImage,
            pauseButtonIcon: pauseButtonIcon,
            playButtonIcon: playButtonIcon,
            leavingModalTitle: playConfig.leavingModalTitle,
            leaveButtonText: playConfig.leaveButtonText,
            cancelButtonText: playConfig.cancelButtonText,
        };
    }

    /**
     * 获取共情音频流
     */
    static async getEmpathyAudio(playSessionId: number): Promise<string | null> {
        try {
            // 验证会话权限
            const playSession = await PlaySession.findByPk(playSessionId);
            if (!playSession) {
                throw new AppError('会话不存在', 404);
            }
            // 尝试从Redis缓存获取
            const cacheKey = `empathy_audio:${playSessionId}`;
            const cachedResult = await RedisService.get(cacheKey);

            if (cachedResult) {
                try {
                    const audioResult = JSON.parse(cachedResult);
                    if (audioResult.audio_url && audioResult.audio_url.startsWith('data:audio/wav;base64,')) {
                        // 提取base64数据部分
                        const base64Audio = audioResult.audio_url.replace('data:audio/wav;base64,', '');
                        logger.info(`Retrieved empathy audio from Redis for playSessionId: ${playSessionId}`);
                        return base64Audio;
                    }
                } catch (parseError) {
                    logger.error(`Failed to parse cached empathy audio for playSessionId: ${playSessionId}`, parseError);
                    // 删除损坏的缓存
                    await RedisService.del(cacheKey);
                }
            }

            // 缓存不存在或已损坏
            logger.warn(`Empathy audio cache not found or corrupted for playSessionId: ${playSessionId}`);
            return null;

        } catch (error) {
            logger.error(`Error getting empathy audio for playSessionId ${playSessionId}:`, error);
            if (error instanceof AppError) {
                throw error;
            }
            throw new AppError('获取音频失败', 500);
        }
    }

    /**
     * 异步生成工作流响应
     * @param userId 用户ID
     * @param playSessionId 播放会话ID
     * @returns 是否成功触发生成
     */
    static async generateWorkflowResponse(userId: number, playSessionId: number): Promise<boolean> {
        try {
            // 生成版本戳并设置为当前最新版本
            const versionTimestamp = Date.now();
            const versionKey = `workflow_version:${playSessionId}`;
            const versionSetSuccess = await RedisService.set(versionKey, versionTimestamp.toString(), 300); // 5分钟过期
            if (!versionSetSuccess) {
                logger.error(`Failed to set workflow version key ${versionKey} for playSessionId: ${playSessionId}`);
                throw new AppError('请稍后重试', 500);
            }
            logger.info(`Generated new workflow version ${versionTimestamp} for playSessionId: ${playSessionId}`);

            const playSession = await PlaySessionService.get(playSessionId);
            if (!playSession) {
                throw new AppError('Play session not found', 404);
            }
            if (playSession.userId !== userId) {
                throw new AppError('Play session not found or access denied', 404);
            }

            // 获取当前选择的目标和情绪
            const selectedGoals = playSession.suggestedGoals?.filter(g => g.isSelected);
            if (!selectedGoals || selectedGoals.length === 0) {
                throw new AppError('No selected goal found for this session. Please select a goal first.', 400);
            }
            const currentGoal = selectedGoals[0];

            // 检查Redis中是否已有缓存的工作流数据
            const cacheKey = `workflow_audio:${playSessionId}`;
            const cachedResult = await RedisService.get(cacheKey);

            if (cachedResult) {
                try {
                    const existingResponse: ProcessedProcessWorkflowResult & { goalKey?: string } = JSON.parse(cachedResult);

                    // 检查缓存中的goal是否与当前选择的goal一致
                    if (existingResponse.goalKey && existingResponse.goalKey === currentGoal.goalKey) {
                        logger.info(`Using cached workflow response from Redis for playSessionId: ${playSessionId}, goal: ${currentGoal.goalKey}, version: ${versionTimestamp}`);

                        // 缓存命中且goal一致，更新版本戳到当前版本
                        await RedisService.set(versionKey, versionTimestamp.toString(), 300);

                        // 缓存命中且goal一致，直接返回
                        return true;
                    } else {
                        logger.info(`Goal changed from ${existingResponse.goalKey} to ${currentGoal.goalKey} for playSessionId: ${playSessionId}, clearing old workflow cache`);

                        // Goal不一致，删除旧缓存
                        await RedisService.del(cacheKey);
                    }
                } catch (parseError) {
                    logger.warn(`Failed to parse cached workflow response for playSessionId: ${playSessionId}, regenerating...`);
                    // 删除损坏的缓存
                    await RedisService.del(cacheKey);
                }
            }

            const detectedEmotions = playSession.detectedEmotions;
            if (!detectedEmotions || detectedEmotions.length === 0) {
                throw new AppError('No detected emotion found for this session. Please select an emotion first.', 400);
            }
            const selectedEmotions = detectedEmotions.filter(e => e.isSelected);
            if (!selectedEmotions || selectedEmotions.length === 0) {
                throw new AppError('No selected emotion found for this session. Please select an emotion first.', 400);
            }

            const primaryGoal = currentGoal;
            const primaryEmotion = selectedEmotions[0];
            if (!primaryEmotion) {
                throw new AppError('No primary emotion found for this session. Please select an emotion first.', 400);
            }
            const unselectedEmotions = detectedEmotions.filter(e => !e.isSelected);

            // 构建 emotion 参数字符串 (选中的情绪)
            const emotionStrings = selectedEmotions.map(e => {
                return e.emotionKey;
            });
            const emotionParam = emotionStrings.join(',');

            // 构建 no_emotion 参数字符串 (未选中的情绪)
            const noEmotionStrings = unselectedEmotions.map(e => {
                return e.emotionKey;
            });
            const noEmotionParam = noEmotionStrings.join(',');

            const voiceId = await this.getUserVoiceId(userId);

            // 固定 output_type 和 generate_audio
            const finalOutputType = 'audio';
            const generateAudioFlag = 'true';

            const aiParams: ProcessWorkflowParams = {
                text: playSession.inputText,
                goal: primaryGoal.goalKey,
                emotion: emotionParam,
                no_emotion: noEmotionParam,
                voice_id: voiceId,
                output_type: finalOutputType,
                generate_audio: generateAudioFlag,
            };
            logger.info(`generateWorkflowResponse aiParams ${JSON.stringify(aiParams)}`);

            // 调用AI服务获取音频数据
            const aiCallStart = Date.now();
            const aiResult = await PartnerAiService.processWorkflow(aiParams);
            const aiCallDuration = Date.now() - aiCallStart;

            if (!aiResult.audio) {
                throw new AppError('Audio base64 is missing in AI workflow response', 500);
            }

            // 在保存结果前检查版本戳，确保当前版本仍然是最新的
            const currentVersion = await RedisService.get(versionKey);
            if (!currentVersion || parseInt(currentVersion) !== versionTimestamp) {
                logger.info(`Workflow version outdated for playSessionId: ${playSessionId}, current: ${currentVersion}, expected: ${versionTimestamp}, skipping save`);
                return false; // 版本已过期，不保存结果
            }

            // 构建结果对象，包含base64音频数据和goalKey用于缓存验证
            const processedResult: ProcessedProcessWorkflowResult & { goalKey: string } = {
                text: aiResult.text,
                tts_text: aiResult.tts_text,
                description: aiResult.description,
                audio_url: `data:audio/wav;base64,${aiResult.audio}`, // 使用data URL格式
                generated_at: new Date().toISOString(),
                ai_call_duration: aiCallDuration,
                goalKey: primaryGoal.goalKey // 添加goalKey用于下次缓存验证
            };

            // 再次检查版本戳（双重检查，防止在构建结果时版本被更新）
            const finalVersion = await RedisService.get(versionKey);
            if (!finalVersion || parseInt(finalVersion) !== versionTimestamp) {
                logger.info(`Workflow version outdated during save for playSessionId: ${playSessionId}, current: ${finalVersion}, expected: ${versionTimestamp}, skipping save`);
                return false; // 版本已过期，不保存结果
            }

            // 缓存到Redis，TTL设置为15分钟（900秒）
            const cacheValue = JSON.stringify(processedResult);
            await RedisService.set(cacheKey, cacheValue, 900);
            logger.info(`Workflow response cached to Redis for playSessionId: ${playSessionId}, version: ${versionTimestamp}, TTL: 15 minutes`);

            // 更新数据库，存储不含base64的元数据版本
            const dbResult = {
                ...processedResult,
                audio_url: `redis:${cacheKey}` // 标记数据在Redis中
            };
            await PlaySessionService.updatePlaySession(playSessionId, 'respWorkflow', JSON.stringify(dbResult));

            return true;
        } catch (error) {
            logger.error(`generateWorkflowResponse error ${error}`);
            return false;
        }
    }

    /**
     * 获取工作流状态
     * @param userId 用户ID
     * @param playSessionId 播放会话ID
     * @returns 工作流音频URL，如果未准备好则返回空字符串
     */
    static async getWorkflowStatus(userId: number, playSessionId: number): Promise<{ workflowAudioUrl: string }> {
        try {
            const playSession = await PlaySession.findByPk(playSessionId);
            if (!playSession || playSession.userId !== userId) {
                throw new AppError('Play session not found or access denied', 404);
            }

            // 查询用户选择的目标
            const selectedGoals = await SuggestedGoal.findAll({
                where: { playSessionId: playSessionId, isSelected: true }
            });
            if (!selectedGoals || selectedGoals.length === 0) {
                return { workflowAudioUrl: '' }; // 没有选择目标，返回空URL
            }
            const primaryGoal = selectedGoals[0];

            let workflowResult: ProcessedProcessWorkflowResult = JSON.parse(playSession.respWorkflow || '{}');
            if (!workflowResult || !workflowResult.audio_url) {
                return { workflowAudioUrl: '' }; // 工作流响应不存在，返回空URL
            }

            let audioUrl = '';
            // 检查是否存在音频缓存，并验证缓存内容与当前选择的目标是否一致
            if (workflowResult.audio_url?.startsWith('redis:')) {
                const cacheKey = workflowResult.audio_url.replace('redis:', '');
                logger.info(`getWorkflowStatus cacheKey ${cacheKey}`);
                const cachedResult = await RedisService.get(cacheKey);

                if (cachedResult) {
                    try {
                        // 解析缓存内容，检查goalKey是否一致
                        const cachedAudioResult = JSON.parse(cachedResult);
                        const cachedGoalKey = cachedAudioResult.goalKey;

                        if (cachedGoalKey && cachedGoalKey === primaryGoal.goalKey) {
                            // 缓存中的goalKey与当前选择的goalKey一致，返回音频URL
                            audioUrl = `/play-session/${playSessionId}/workflow-audio`;
                            logger.info(`Workflow audio cache available and goal matches for playSessionId: ${playSessionId}, goalKey: ${primaryGoal.goalKey}`);
                        } else {
                            // 缓存中的goalKey与当前选择的goalKey不一致，返回空URL
                            audioUrl = '';
                            logger.info(`Workflow audio cache exists but goal mismatch for playSessionId: ${playSessionId}, cached: ${cachedGoalKey}, current: ${primaryGoal.goalKey}`);
                        }
                    } catch (parseError) {
                        logger.warn(`Failed to parse cached workflow audio result for playSessionId: ${playSessionId}, returning empty audioUrl`);
                        audioUrl = '';
                    }
                } else {
                    logger.warn(`Workflow audio cache expired for playSessionId: ${playSessionId}`);
                    audioUrl = ''; // 缓存过期，返回空URL
                }
            }

            return { workflowAudioUrl: audioUrl };

        } catch (error) {
            logger.error(`Error getting workflow status for playSessionId ${playSessionId}:`, error);
            if (error instanceof AppError) {
                throw error;
            }
            throw new AppError('获取工作流状态失败', 500);
        }
    }

    /**
     * 获取工作流音频数据
     * @param playSessionId 会话ID
     * @returns base64格式的音频数据
     */
    static async getWorkflowAudio(playSessionId: number): Promise<string | null> {
        try {
            // 验证会话权限
            const playSession = await PlaySession.findByPk(playSessionId);
            if (!playSession) {
                throw new AppError('会话不存在', 404);
            }
            // 尝试从Redis缓存获取
            const cacheKey = `workflow_audio:${playSessionId}`;
            const cachedResult = await RedisService.get(cacheKey);

            if (cachedResult) {
                try {
                    const audioResult = JSON.parse(cachedResult);
                    if (audioResult.audio_url && audioResult.audio_url.startsWith('data:audio/wav;base64,')) {
                        // 提取base64数据部分
                        const base64Audio = audioResult.audio_url.replace('data:audio/wav;base64,', '');
                        logger.info(`Retrieved workflow audio from Redis for playSessionId: ${playSessionId}`);
                        return base64Audio;
                    }
                } catch (parseError) {
                    logger.error(`Failed to parse cached workflow audio for playSessionId: ${playSessionId}`, parseError);
                    // 删除损坏的缓存
                    await RedisService.del(cacheKey);
                }
            }

            // 缓存不存在或已损坏
            logger.warn(`Workflow audio cache not found or corrupted for playSessionId: ${playSessionId}`);
            return null;

        } catch (error) {
            logger.error(`Error getting workflow audio for playSessionId ${playSessionId}:`, error);
            if (error instanceof AppError) {
                throw error;
            }
            throw new AppError('获取工作流音频失败', 500);
        }
    }
}
