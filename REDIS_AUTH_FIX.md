# Redis 认证问题修复指南

## 问题描述

生产环境中出现以下Redis错误：
```
[ioredis] Unhandled error event: ReplyError: DENIED Redis is running in protected mode because protected mode is enabled and no password is set for the default user.
```

## 问题原因

1. Redis生产环境配置启用了 `protected-mode yes`
2. 但没有设置密码认证 (`requirepass`)
3. 客户端连接时没有提供密码

当Redis启用保护模式但没有密码时，只允许本地回环接口连接，拒绝外部连接。

## 解决方案

### 1. 已修复的文件

以下文件已经更新以支持Redis密码认证：

- `redis/redis.conf` - 添加了 `requirepass` 配置
- `.env.production` - 添加了 `REDIS_PASSWORD` 环境变量
- `docker-compose.prod.yml` - 传递密码环境变量并更新健康检查
- `backend/src/services/redis.service.ts` - 支持密码认证
- `redis/README.md` - 更新文档说明

### 2. 部署步骤

#### 步骤1: 停止当前Redis服务
```bash
docker-compose -f docker-compose.prod.yml stop redis
```

#### 步骤2: 清理Redis数据（可选，如果需要清空缓存）
```bash
docker-compose -f docker-compose.prod.yml down redis
docker volume rm moodplay-deploy_redis_data  # 根据实际volume名称调整
```

#### 步骤3: 重新启动Redis服务
```bash
docker-compose -f docker-compose.prod.yml up -d redis
```

#### 步骤4: 验证Redis连接
```bash
# 检查容器状态
docker ps | grep redis

# 测试密码认证
docker exec redis redis-cli -a moodplay_redis_2024_secure_password ping
# 应该返回: PONG

# 测试无密码连接（应该失败）
docker exec redis redis-cli ping
# 应该返回: (error) NOAUTH Authentication required.
```

#### 步骤5: 重启后端服务
```bash
docker-compose -f docker-compose.prod.yml restart backend
```

#### 步骤6: 验证应用连接
```bash
# 查看后端日志，确认Redis连接成功
docker-compose -f docker-compose.prod.yml logs -f backend
```

### 3. 安全注意事项

1. **密码强度**: 当前使用的密码 `moodplay_redis_2024_secure_password` 是示例密码，建议在生产环境中使用更强的密码。

2. **密码管理**: 
   - 确保 `.env.production` 文件不被提交到版本控制
   - 考虑使用密钥管理服务
   - 定期更换密码

3. **网络安全**: 
   - Redis仍然绑定到 `0.0.0.0`，但在Docker网络中是安全的
   - 确保Redis端口不直接暴露到公网

### 4. 故障排除

#### 问题1: 连接超时
```bash
# 检查Redis容器是否正常运行
docker ps | grep redis
docker logs redis
```

#### 问题2: 认证失败
```bash
# 确认密码配置正确
docker exec redis redis-cli -a moodplay_redis_2024_secure_password CONFIG GET requirepass
```

#### 问题3: 后端连接失败
```bash
# 检查环境变量是否正确传递
docker exec moodplay-backend env | grep REDIS
```

### 5. 监控和维护

#### 定期检查Redis状态
```bash
# 内存使用情况
docker exec redis redis-cli -a moodplay_redis_2024_secure_password INFO memory

# 连接数
docker exec redis redis-cli -a moodplay_redis_2024_secure_password INFO clients

# 性能统计
docker exec redis redis-cli -a moodplay_redis_2024_secure_password INFO stats
```

#### 日志监控
```bash
# 监控Redis日志
docker logs -f redis

# 监控后端Redis相关日志
docker logs -f moodplay-backend | grep -i redis
```

## 总结

通过添加Redis密码认证，解决了保护模式下的连接问题。这个修复：

1. ✅ 解决了 "DENIED Redis is running in protected mode" 错误
2. ✅ 提高了Redis的安全性
3. ✅ 保持了现有的性能优化配置
4. ✅ 向后兼容开发和测试环境

修复完成后，生产环境的Redis将要求密码认证，同时保持高性能的缓存配置。
